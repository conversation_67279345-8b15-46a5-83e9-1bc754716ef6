import asyncio

from agent.compiler.compiler import Compiler
from agent.executor.executor import Executor

SAMPLE_PLAN = """
# This plan demonstrates the use of a stateful bash session.

# Start a bash session.
s = bash_start()

# Run a command to create a file in the session's container.
# The output of this command will be the stdout/stderr.
create_file_output = bash_run(session=s, command="touch /data/session_file.txt")

# Run another command to list files. We should see the new file.
list_files_output = bash_run(session=s, command="ls -l /data")

# Stop the session. This will also stop and remove the container.
bash_stop(session=s)
"""

async def main():
    """
    The main entrypoint for the agent.
    """
    print("--- STARTING AGENT --- kilograms")
    print("Plan:\n" + SAMPLE_PLAN)

    # 1. Instantiate the components
    compiler = Compiler()
    executor = Executor()

    # 2. Compile the plan into an execution graph
    print("--- COMPILING PLAN ---")
    execution_graph = compiler.compile(SAMPLE_PLAN)

    # 3. Execute the graph
    print("--- EXECUTING PLAN ---")
    final_results = await executor.execute(execution_graph, compiler)

    print("\n--- FINAL RESULTS ---")
    print(final_results)

if __name__ == "__main__":
    asyncio.run(main())
