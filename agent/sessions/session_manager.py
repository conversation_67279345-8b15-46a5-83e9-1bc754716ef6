import asyncio
import docker
import uuid
from typing import Dict

from agent.sessions.session import Session, BashSession

class SessionManager:
    """
    Manages the lifecycle of stateful sessions (e.g., bash, ssh)
    by controlling long-running Docker containers.
    """
    def __init__(self):
        try:
            self.docker_client = docker.from_env()
        except docker.errors.DockerException:
            print("ERROR: Docker is not running or not installed.")
            raise
        self.sessions: Dict[str, Session] = {}

    async def create_session(self, tool_type: str) -> str:
        """
        Creates a new stateful session by starting a dedicated, long-running container.
        """
        if tool_type != 'bash':
            raise ValueError(f"Unsupported session type: {tool_type}")

        session_id = str(uuid.uuid4())
        
        def _create_container():
            return self.docker_client.containers.run(
                image="compiler-agent-env",
                command="tail -f /dev/null",
                detach=True,
                remove=True,
            )
        
        container = await asyncio.to_thread(_create_container)

        session = BashSession(
            session_id=session_id,
            tool_type=tool_type,
            container_id=container.id
        )
        self.sessions[session_id] = session
        print(f"SESSION: Created bash session {session_id} in container {container.id[:12]}")
        return session_id

    async def execute_in_session(self, session_id: str, command: str) -> str:
        """
        Executes a command inside a running session container.
        """
        if session_id not in self.sessions:
            raise ValueError(f"Session {session_id} not found.")
        
        session = self.sessions[session_id]
        
        def _exec_run():
            container = self.docker_client.containers.get(session.container_id)
            exit_code, output_bytes = container.exec_run(command)
            
            output = output_bytes.decode('utf-8')
            if exit_code != 0:
                output += f"\nERROR (Exit Code: {exit_code})"
            return output.strip()

        return await asyncio.to_thread(_exec_run)

    async def close_session(self, session_id: str):
        """
        Closes a session and stops its container.
        """
        if session_id not in self.sessions:
            print(f"SESSION: Attempted to close non-existent session {session_id}")
            return

        session = self.sessions.pop(session_id)
        
        def _stop_container():
            try:
                container = self.docker_client.containers.get(session.container_id)
                print(f"SESSION: Closing session {session_id} and stopping container {container.id[:12]}")
                container.stop()
            except docker.errors.NotFound:
                print(f"SESSION: Container for session {session_id} already removed.")
        
        await asyncio.to_thread(_stop_container)