print(__file__)
import asyncio
import ast
import docker
import os
from pathlib import Path
from graphlib import <PERSON><PERSON><PERSON>orter
from typing import Dict, Any, List

from agent.compiler.compiler import Compiler
from agent.dsl.tools import TOOL_REGISTRY
from agent.sessions.session_manager import SessionManager

print("Defining Executor class...")
class Executor:
    """
    Executes a compiled task graph (DAG), handling both stateless and
    stateful tools.
    """

    def __init__(self):
        # This is a shared client for both stateless and stateful container operations
        try:
            self.docker_client = docker.from_env()
        except docker.errors.DockerException:
            print("ERROR: Docker is not running or not installed.")
            raise
        
        self.session_manager = SessionManager()
        
        # Define the shared data directory on the host
        self.host_data_dir = Path(os.getcwd()) / "data"
        self.host_data_dir.mkdir(exist_ok=True)
        print(f"INFO: Shared data directory is: {self.host_data_dir}")

    def _run_stateless_in_container(self, command: str) -> str:
        """
        Blocking function to run a command in a new, ephemeral Docker container.
        """
        print(f"DOCKER (Stateless): Running command: '{command}'")
        try:
            container_output = self.docker_client.containers.run(
                image="compiler-agent-env",
                command=f"/bin/bash -c '{command}'",
                volumes={str(self.host_data_dir): {"bind": "/data", "mode": "rw"}},
                working_dir="/data",
                remove=True,
                stdout=True,
                stderr=True,
            )
            log = container_output.decode('utf-8').strip()
            print(f"DOCKER (Stateless): Log:\n---\n{log}\n---")
            return log
        except docker.errors.ContainerError as e:
            print(f"DOCKER ERROR: Command failed in container.")
            print(f"Stderr:\n{e.stderr.decode('utf-8')}")
            raise

    async def _run_stateless_task(self, func, args, kwargs):
        """
        Asynchronously runs a stateless task in a new Docker container.
        """
        command_dict = func(*args, **kwargs)
        command = command_dict["command"]
        await asyncio.to_thread(self._run_stateless_in_container, command)
        return str(self.host_data_dir / command_dict["output_path"])

    def _resolve_args(self, call_node: ast.Call, results: Dict[str, Any]) -> tuple[List[Any], Dict[str, Any]]:
        """Resolves task arguments from previous results."""
        args = []
        kwargs = {}
        for arg in call_node.args:
            if isinstance(arg, ast.Name):
                args.append(results[arg.id])
            elif isinstance(arg, ast.Constant):
                args.append(arg.value)
            else:
                raise TypeError(f"Unsupported positional argument type: {type(arg)}")

        for kw in call_node.keywords:
            if isinstance(kw.value, ast.Name):
                # If the arg is a variable, it's a result from a previous step
                prev_result = results[kw.value.id]
                # For now, we assume results that are paths need to be translated
                # to container paths. This logic may need to be more robust.
                if isinstance(prev_result, str) and Path(prev_result).is_absolute():
                     # We need to pass the path *inside the container* to the next command
                    host_path = Path(prev_result)
                    # This makes the assumption that the host path is in our shared data dir
                    container_path = f"/data/{host_path.name}"
                    kwargs[kw.arg] = container_path
                else:
                    # Otherwise, pass the result as-is (e.g., a session ID)
                    kwargs[kw.arg] = prev_result
            elif isinstance(kw.value, ast.Constant):
                kwargs[kw.arg] = kw.value.value
            else:
                raise TypeError(f"Unsupported keyword argument value type: {type(kw.value)}")
        return args, kwargs

    async def execute(self, sorter: TopologicalSorter, compiler: Compiler):
        """
        Executes the tasks in the graph in topological order.
        """
        results: Dict[str, Any] = {}

        while sorter.is_active():
            ready_tasks = sorter.get_ready()
            if not ready_tasks:
                raise ValueError("Execution failed: No tasks are ready to run, but the graph is not done.")

            print(f"--- EXECUTING BATCH: {ready_tasks} ---")

            async_tasks = []
            for task_name in ready_tasks:
                call_node = compiler.task_map[task_name]
                func_name = call_node.func.id
                
                if not TOOL_REGISTRY.get(func_name):
                    raise ValueError(f"Tool '{func_name}' not found in registry.")

                args, kwargs = self._resolve_args(call_node, results)

                # Route to the correct handler based on the function name
                if func_name == 'bash_start':
                    task = self.session_manager.create_session('bash')
                elif func_name == 'bash_run':
                    session_id = kwargs.pop('session')
                    command = kwargs.pop('command')
                    task = self.session_manager.execute_in_session(session_id, command)
                elif func_name == 'bash_stop':
                    session_id = kwargs.pop('session')
                    task = self.session_manager.close_session(session_id)
                else:
                    # Default to stateless tool execution
                    func = TOOL_REGISTRY[func_name]
                    task = self._run_stateless_task(func, args, kwargs)
                
                async_tasks.append(task)

            batch_results = await asyncio.gather(*async_tasks)

            for task_name, result in zip(ready_tasks, batch_results):
                results[task_name] = result
                sorter.done(task_name)

        # Clean up any open sessions at the end of execution
        for session_id in list(self.session_manager.sessions.keys()):
            print(f"--- AUTO-CLOSING LEFTOVER SESSION: {session_id} ---")
            await self.session_manager.close_session(session_id)

        print("--- EXECUTION FINISHED ---")
        return results
