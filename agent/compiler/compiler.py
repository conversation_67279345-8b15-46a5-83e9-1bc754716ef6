import ast
import graphlib
from typing import Dict, Any, Set

class Compiler:
    """
    Parses Python DSL code and compiles it into a dependency graph (DAG).
    """

    def compile(self, code_string: str) -> graphlib.TopologicalSorter:
        """
        Takes a string of Python DSL code and returns a topologically sorted graph of tasks.

        Args:
            code_string: The Python DSL code to compile.

        Returns:
            A TopologicalSorter instance representing the execution graph.
        """
        # Parse the code into an Abstract Syntax Tree (AST)
        tree = ast.parse(code_string)

        # The graph will store dependencies. The key is the task (variable name),
        # and the value is a set of dependencies (other variable names).
        graph: Dict[str, Set[str]] = {}
        # Task map stores the actual function call details for each task.
        self.task_map: Dict[str, ast.Call] = {}

        # Traverse the AST to build the graph
        for node in ast.walk(tree):
            call_node = None
            dependencies = set()

            if isinstance(node, ast.Assign) and isinstance(node.value, ast.Call):
                # <PERSON><PERSON> calls in assignments, e.g., result = tool()
                task_name = node.targets[0].id
                call_node = node.value

            elif isinstance(node, ast.Expr) and isinstance(node.value, ast.Call):
                # <PERSON>le calls not in assignments, e.g., tool()
                # We generate a unique name for this task to put it in the graph.
                task_name = f"expr_{node.lineno}_{node.col_offset}"
                call_node = node.value

            if call_node:
                self.task_map[task_name] = call_node
                # Find dependencies by looking at the arguments of the function call
                for arg in call_node.args:
                    if isinstance(arg, ast.Name):
                        dependencies.add(arg.id)
                for kw in call_node.keywords:
                    if isinstance(kw.value, ast.Name):
                        dependencies.add(kw.value.id)
                
                graph[task_name] = dependencies

        # Create the graph and perform a topological sort
        sorter = graphlib.TopologicalSorter(graph)
        sorter.prepare()
        return sorter
