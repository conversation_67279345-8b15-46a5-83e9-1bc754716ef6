"""
This module defines the stateful tools available to the agent.

Unlike stateless tools, these functions do not return shell commands.
They are markers that the executor interprets to interact with the SessionManager.
"""

def bash_start():
    """
    Starts a new, persistent bash session in a dedicated container.
    
    Returns:
        A unique session ID.
    """
    # This function is a placeholder. The executor will have special logic
    # to call SessionManager.create_session('bash').
    pass

def bash_run(session: str, command: str):
    """
    Runs a command within an existing bash session.

    Args:
        session: The ID of the session to use.
        command: The shell command to execute.

    Returns:
        The stdout/stderr from the command.
    """
    # This function is a placeholder. The executor will have special logic
    # to call SessionManager.execute_in_session().
    pass

def bash_stop(session: str):
    """
    Stops a bash session and its container.

    Args:
        session: The ID of the session to stop.
    """
    # This function is a placeholder. The executor will have special logic
    # to call SessionManager.close_session().
    pass


STATEFUL_TOOL_REGISTRY = {
    "bash_start": bash_start,
    "bash_run": bash_run,
    "bash_stop": bash_stop,
}
