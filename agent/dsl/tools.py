"""
This module defines the tools available to the agent.

Each tool is a function that should return a dictionary containing the
command to be executed in a shell environment.
"""

def download_sra_data(accession: str):
    """
    Generates the command to download SRA data using the sra-toolkit.
    It uses prefetch to download the .sra file and then fastq-dump to convert it to fastq.
    """
    # The command will be executed in the /data directory of the container.
    command = f"prefetch {accession} && fastq-dump --split-files --outdir . {accession}"
    
    # Define the expected output path relative to the /data directory.
    # This helps the executor know where to find the result.
    output_path = f"./{accession}_1.fastq" # Assuming paired-end, we just track one file.
    
    return {
        "command": command,
        "output_path": output_path
    }

def run_fastqc(reads_path: str):
    """
    Generates the command to run FastQC on a given FASTQ file.
    """
    # The reads_path will be a path inside the container's /data directory.
    command = f"fastqc {reads_path}"
    
    # Define the expected output path. FastQC creates a .html file.
    # We derive the output name from the input name.
    output_filename = reads_path.split('/')[-1].replace('.fastq', '_fastqc.html')
    output_path = f"./{output_filename}"

    return {
        "command": command,
        "output_path": output_path
    }

def download_genome(genome_name: str, url: str):
    """
    Generates the command to download a gzipped FASTA genome and unzip it.
    """
    output_filename = f"{genome_name}.fna"
    command = f"wget -O {output_filename}.gz {url} && gunzip {output_filename}.gz"
    return {
        "command": command,
        "output_path": f"./{output_filename}"
    }

def index_genome_bwa(genome_path: str):
    """
    Generates the command to index a reference genome using bwa index.
    """
    command = f"bwa index {genome_path}"
    # BWA index doesn't create a single output file, it creates multiple files.
    # We'll return the original genome path as the result for dependency tracking.
    return {
        "command": command,
        "output_path": genome_path
    }

def align_reads_bwa(genome_path: str, reads_path: str):
    """
    Generates the command to align paired-end reads to a reference genome using bwa mem.
    It infers the path of the second reads file (_2.fastq) from the first (_1.fastq).
    """
    # Infer the second reads file path
    reads_path_2 = reads_path.replace('_1.fastq', '_2.fastq')
    output_filename = "aligned_reads.sam"
    command = f"bwa mem {genome_path} {reads_path} {reads_path_2} > {output_filename}"
    return {
        "command": command,
        "output_path": f"./{output_filename}"
    }

# The TOOL_REGISTRY now maps DSL function names to these command-generating functions.
TOOL_REGISTRY = {
    "download_sra_data": download_sra_data,
    "run_fastqc": run_fastqc,
    "download_genome": download_genome,
    "index_genome_bwa": index_genome_bwa,
    "align_reads_bwa": align_reads_bwa,
}

from .stateful_tools import STATEFUL_TOOL_REGISTRY

# Combine stateless and stateful tool registries for a single point of access
TOOL_REGISTRY.update(STATEFUL_TOOL_REGISTRY)
