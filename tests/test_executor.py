
import os
import unittest
from unittest.mock import patch, MagicMock, AsyncMock, call
from pathlib import Path

from agent.compiler.compiler import Compiler
from agent.executor.executor import Executor

class TestExecutor(unittest.IsolatedAsyncioTestCase):

    @patch('docker.from_env')
    async def test_execution_order_and_argument_passing(self, mock_docker_from_env):
        """
        Tests that a simple dependent workflow executes in the correct order
        and that file paths are passed correctly between tasks.
        """
        # 1. Setup Mocks
        # Mock Docker client to avoid real container operations
        mock_docker_client = mock_docker_from_env.return_value
        mock_docker_client.containers.run.return_value.decode.return_value = "docker log"

        # Mock tool functions
        mock_task_one = MagicMock(return_value={"command": "cmd1", "output_path": "./out1.txt"})
        mock_task_two = MagicMock(return_value={"command": "cmd2", "output_path": "./out2.txt"})
        
        # Create a mock for TOOL_REGISTRY
        mock_tool_registry = MagicMock()
        mock_tool_registry.get.side_effect = lambda key: {"task_one": mock_task_one, "task_two": mock_task_two}.get(key)

        # Create a mock for SessionManager
        mock_session_manager = MagicMock()

        # Create a mock for Executor
        mock_executor = MagicMock(spec=Executor)
        mock_executor.host_data_dir = Path(os.getcwd()) / "data" # Set host_data_dir for mock_executor
        mock_executor.container_data_dir = '/data' # Set container_data_dir for mock_executor
        mock_executor._run_stateless_task = AsyncMock() # Mock the internal method

        # Configure the mock_executor's execute method
        mock_executor.execute.side_effect = lambda sorter, compiler: Executor.execute(mock_executor, sorter, compiler) # Use the real execute method

        # Replace the Executor in the module with our mock
        with patch('agent.executor.executor.Executor', new=mock_executor):
            executor = mock_executor # Use the mock_executor

            # Mock the _run_stateless_task method of the Executor to return a simple string
        # This avoids issues with MagicMock's default behavior for nested attribute access
        mock_executor._run_stateless_task.side_effect = [
            str(Path(mock_executor.host_data_dir) / "out1.txt"),
            str(Path(mock_executor.host_data_dir) / "out2.txt"),
        ]

                # 2. Define plan and compile
                plan = """
a = task_one()
b = task_two(input_path=a)
"""
                compiler = Compiler()
                sorter = compiler.compile(plan)

                # 3. Execute
                results = await executor.execute(sorter, compiler)

                # 4. Assertions
                # Assert that the stateless task runner was called with the correct arguments
                mock_run_stateless_task.assert_has_calls([
                    call(mock_task_one, [], {}),
                    call(mock_task_two, [], {'input_path': f"/data/{Path(mock_task_one.return_value['output_path']).name}"}),
                ])

                # Assert final results dictionary is correct
                self.assertTrue(results['a'].endswith("data/out1.txt"))
                self.assertTrue(results['b'].endswith("data/out2.txt"))

    @patch('docker.from_env')
    async def test_parallel_execution(self, mock_docker_from_env):
        """Tests that independent tasks are executed in parallel."""
        # 1. Setup Mocks
        mock_docker_client = mock_docker_from_env.return_value
        mock_docker_client.containers.run.return_value.decode.return_value = "docker log"

        mock_task_one = MagicMock(return_value={"command": "cmd1", "output_path": "./out1.txt"})
        mock_task_two = MagicMock(return_value={"command": "cmd2", "output_path": "./out2.txt"})
        mock_task_three = MagicMock(return_value={"command": "cmd3", "output_path": "./out3.txt"})

        # Create a mock for TOOL_REGISTRY
        mock_tool_registry = MagicMock()
        mock_tool_registry.get.side_effect = lambda key: {
            "task_one": mock_task_one, 
            "task_two": mock_task_two, 
            "task_three": mock_task_three
        }.get(key)

        # Create a mock for SessionManager
        mock_session_manager = MagicMock()

        # Create a mock for Executor
        mock_executor = MagicMock(spec=Executor)
        mock_executor.host_data_dir = Path(os.getcwd()) / "data" # Set host_data_dir for mock_executor
        mock_executor.container_data_dir = '/data' # Set container_data_dir for mock_executor
        mock_executor._run_stateless_task = AsyncMock() # Mock the internal method

        # Configure the mock_executor's execute method
        mock_executor.execute.side_effect = lambda sorter, compiler: Executor.execute(mock_executor, sorter, compiler) # Use the real execute method

        # Replace the Executor in the module with our mock
        with patch('agent.executor.executor.Executor', new=mock_executor):
            executor = mock_executor # Use the mock_executor

            mock_executor._run_stateless_task.side_effect = [
            str(Path(mock_executor.host_data_dir) / "out1.txt"),
            str(Path(mock_executor.host_data_dir) / "out2.txt"),
            str(Path(mock_executor.host_data_dir) / "out3.txt"),
        ]

                # 2. Define plan and compile
                plan = """
a = task_one()
b = task_two()
c = task_three(in1=a, in2=b)
"""
                compiler = Compiler()
                sorter = compiler.compile(plan)

                # 3. Execute
                await executor.execute(sorter, compiler)

                # 4. Assertions
                # Assert that the stateless task runner was called with the correct arguments
                mock_run_stateless_task.assert_has_calls([
                    call(mock_task_one, [], {}),
                    call(mock_task_two, [], {}),
                    call(mock_task_three, [], {'in1': f"/data/{Path(mock_task_one.return_value['output_path']).name}", 'in2': f"/data/{Path(mock_task_two.return_value['output_path']).name}"}),
                ], any_order=True)

                # Assert that the final task (task_three) was called with the correct container paths
                expected_path1 = f"/data/{Path(mock_task_one.return_value['output_path']).name}"
                expected_path2 = f"/data/{Path(mock_task_two.return_value['output_path']).name}"
                mock_task_three.assert_called_once_with(in1=expected_path1, in2=expected_path2)

    @patch('docker.from_env')
    async def test_tool_not_found(self, mock_docker_from_env):
        """Tests that a ValueError is raised for an unregistered tool."""
        # Create a mock for SessionManager
        mock_session_manager = MagicMock()

        # Create a mock for Executor
        mock_executor = MagicMock(spec=Executor)
        mock_executor.execute.side_effect = lambda sorter, compiler: Executor.execute(mock_executor, sorter, compiler) # Use the real execute method

        # Replace the Executor in the module with our mock
        with patch('agent.executor.executor.Executor', new=mock_executor):
            executor = mock_executor # Use the mock_executor

            plan = "a = unregistered_tool()"
            compiler = Compiler()
            sorter = compiler.compile(plan)

            with self.assertRaisesRegex(ValueError, "Tool 'unregistered_tool' not found in registry"):
                await executor.execute(sorter, compiler)

    @patch('docker.from_env')
    async def test_stateful_bash_workflow(self, mock_docker_from_env):
        """Tests the execution of a stateful bash workflow."""
        # 1. Setup Mocks
        mock_docker_client = mock_docker_from_env.return_value
        mock_docker_client.containers.run.return_value.decode.return_value = "docker log"

        # Create a mock for SessionManager
        mock_session_manager = MagicMock()
        mock_session_manager.create_session.return_value = AsyncMock(return_value="test_session_id")
        mock_session_manager.execute_in_session.side_effect = [
            ("", "", 0), # Output for touch command (stdout, stderr, exit_code)
            ("total 0\n-rw-r--r-- 1 <USER> <GROUP> 0 Aug 27 12:00 session_file.txt", "", 0) # Output for ls command
        ]
        mock_session_manager.close_session.return_value = AsyncMock(return_value=None)

        # Create a mock for TOOL_REGISTRY
        mock_tool_registry = MagicMock()
        mock_tool_registry.get.side_effect = lambda key: {
            "bash_start": MagicMock(),
            "bash_run": MagicMock(),
            "bash_stop": MagicMock(),
        }.get(key)

        # Create a mock for Executor
        mock_executor = MagicMock(spec=Executor)
        mock_executor.execute.side_effect = lambda sorter, compiler: Executor.execute(mock_executor, sorter, compiler) # Use the real execute method

        # Replace the Executor in the module with our mock
        with patch('agent.executor.executor.Executor', new=mock_executor):
            executor = mock_executor # Use the mock_executor

            # 2. Define plan and compile
            plan = """
s = bash_start()
create_file_output = bash_run(session=s, command=\"touch /data/session_file.txt\")
list_files_output = bash_run(session=s, command=\"ls -l /data\")
bash_stop(session=s)
"""
            compiler = Compiler()
            sorter = compiler.compile(plan)

            # 3. Execute
            results = await executor.execute(sorter, compiler)

            # 4. Assertions
            mock_session_manager.create_session.assert_called_once_with('bash')
            mock_session_manager.execute_in_session.assert_has_calls([
                call("test_session_id", "touch /data/session_file.txt"),
                call("test_session_id", "ls -l /data"),
            ])
            mock_session_manager.close_session.assert_called_once_with("test_session_id")

            self.assertEqual(results['s'], "test_session_id")
            self.assertEqual(results['create_file_output'], "")
            self.assertIn("session_file.txt", results['list_files_output'])
            self.assertIsNone(results['expr_15_0']) # bash_stop is a non-assigning call

if __name__ == '__main__':
    unittest.main()
