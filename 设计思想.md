
# AI Compiler Agent 系统设计书

## 1. 项目概述

本项目旨在开发一个通用的 AI Agent 系统，采用 **LLM Compiler** 架构思想，将 AI 的高级规划能力与机器的高效执行能力相结合。该系统能够将复杂任务分解为一系列可执行步骤，并支持并行处理、动态纠错和人类干预。

## 2. 核心架构
系统的核心是一个**编译器（Compiler）**，它负责解析 AI 生成的计划并将其转化为可执行的任务图。整个流程分为三个主要阶段：

- **规划 (Plan)**：由 LLM 负责，将用户的自然语言需求转化为机器可读的执行计划。

  - 一般的Agent使用task list方式生成计划, 我们的Agent使用Python DSL一次性生成计划
  
  - ```
    # task list方式的示例
    Here's my plan to address your request:
    
    1.  [ ] **Download yeast reference genome:**
        *   Search NCBI for "Saccharomyces cerevisiae reference genome".
        *   Identify a suitable assembly (e.g., R64).
        *   Download the FASTA file for the reference genome.
    2.  [ ] **Find and download yeast sequencing data:**
        *   Search NCBI SRA (Sequence Read Archive) for *Saccharomyces cerevisiae* sequencing data.
        *   Select a small, manageable paired-end dataset for demonstration purposes.
        *   Download the FASTQ files for the selected dataset.
    3.  [ ] **Perform Quality Control (QC) on raw reads:**
        *   Use `fastqc` to assess the quality of the downloaded FASTQ files.
    
    ```
  
  - ```python
    # python DSL方式的示例
    # step1:
    s1 = search("Saccharomyces cerevisiae reference genome")
    r1 = LLM("Identify a suitable assembly (e.g., R64).", s1)
    if r1 is correct download url:
        f1 = download(r1)
    else:
       pass
    # goto step1
    # step2:
    s2 = search("NCBI SRA (Sequence Read Archive) for *Saccharomyces cerevisiae* sequencing data")
    r2 = LLM("Select a small, manageable paired-end dataset for demonstration purposes.", s2)
    ...
    	
    ```
  
  - 为了实现python DSL(LLM compiler)方式, 必须有一个**LLM**工具被定义, 这个工具实际是一个仅能对话的Agent. 


- **执行 (Execute)**：一个独立的执行器负责严格按照计划执行任务，并处理并行、异步和状态管理。

- **纠错 (Debug)**：当执行失败或时，错误信息会反馈给 LLM，让其重新规划并修复计划。

这种设计将 AI 的决策层与执行层解耦，确保了系统的安全性、可控性和可扩展性。

## 3. 关键设计点

### 3.1 计划语言：Python DSL

为了实现安全、可控且易于生成的计划，我们选择使用**一个受限的 Python 领域特定语言（DSL）**作为计划语言。

- **设计原则**：AI 只能调用预先定义并注册的工具函数，不能使用任何其他 Python 语法（如 `print`, `import` 等）。

- **AI 友好性**：通过精心设计的提示词（prompting），指导 AI 生成格式清晰的 Python 函数调用序列。

- **代码示例**：

  Python

  ```
  result1 = download_genome(organism="yeast")
  result2 = download_reads(dataset="yeast_data")
  result3 = run_fastqc(reads=result2)
  result4 = align_reads(genome=result1, reads=result3)
  ```

### 3.2 编译与并行化
这一阶段由编译器后端完成，旨在将串行的计划转化为高效的并行任务。

- **工具**：
  - **语法解析**：使用 Python 内置的 `ast` 模块将代码解析为抽象语法树（AST）。
  - **依赖图**：通过遍历 AST，自动分析变量赋值和函数参数，构建一个**有向无环图（DAG）**来表示任务间的依赖关系。
  - **并行调度**：使用 `graphlib.TopologicalSorter` 和 `asyncio`，自动识别图中所有无依赖的节点，并利用异步 I/O 并行执行它们。
- **流程**：编译器会首先分析整个计划，找到所有可以并行的任务，然后调度器会同时启动这些任务，从而避免阻塞和空等。

### 3.3 工具系统：无状态与有状态的统一
为兼容各种类型的任务，工具系统被设计为通用且可扩展。

- **通用接口**：所有工具都通过一个统一的接口与 Agent 交互，该接口负责接收任务信息和返回结果。
- **无状态工具**：适用于即时返回结果的任务，如文件读写、API 调用。
- **有状态工具**：这是本设计的核心创新点，用于管理会话和长时间运行的任务。
  - **概念**：将 SSH 会话、Bash 脚本执行甚至人类交互都抽象为有状态工具。
  - **实现**：使用**会话管理器（Session Manager）**来跟踪每个有状态工具的生命周期和内部状态。
  - **异步处理**：Agent 提交任务后不阻塞，而是由事件驱动模型处理后续输出和完成通知，使 Agent 可以在等待的同时执行其他任务。
  - ### 人机协作：可控的人类干预
  
    - **模型**：将“监听人类输入”也视为一种特殊类型的**有状态工具**。
    - **实现**：Agent 可以在规划中明确地调用这个“监听工具”。当工具被调用时，它会进入等待状态，直到收到人类输入。
    - **优势**：这让 Agent 对干预有了**主动权**，它可以在流程中的特定时刻暂停，等待人类决策或反馈，从而确保人机协作的流畅和可预测。
  
### 3.4 安全与纠错机制
- **沙箱执行**：所有 AI 生成的代码都在严格隔离的沙箱中执行，以确保安全。

  - **方案**：使用 **Docker 容器**。它提供了强大的隔离性，可以精确控制文件系统、网络和资源访问权限。
  - **流程**：编译器将任务打包，在 Docker 容器内启动一个进程执行，完成后将结果返回。

- **动态纠错**：

  - **错误捕获**：执行器会捕获任务执行期间的所有错误，并附带详细的错误信息。
  - **反馈循环**：错误信息会作为上下文反馈给 LLM，让其重新生成一个修复后的计划。
  - **增量执行**：编译器会记住哪些步骤已成功，并从失败点继续执行，避免重复工作。

- **不同沙盒（Docker 容器）间的并行调用**

   1. 核心架构：中央调度器（Task Scheduler）

  - **编译器 (Compiler)：** 仍然负责解析 AI 的计划并生成 **任务图（DAG）**。
  - **任务队列 (Task Queue)：** 这是一个核心组件，用于存储所有待执行的任务。
  - **调度器 (Scheduler)：** 负责从任务队列中提取可执行任务，并将其分发到不同的沙盒（Docker 容器）中。
  - **沙盒执行器 (Sandbox Executor)：** 运行在每个 Docker 容器内的独立进程。它接收来自调度器的任务，执行本地工具，然后将结果返回。
  
   2. 工作流程演示

  **AI 生成的计划：**

  Python

  ```
  result1 = download_genome(organism="yeast")
  result2 = download_reads(dataset="yeast_data")
  result3 = align_reads(genome=result1, reads=result2)
  ```
  
  **步骤一：编译器解析与任务分发**
  
  1. **解析：** 编译器解析上述 DSL，识别出 `download_genome` 和 `download_reads` 两个任务是**独立的、可并行的**，而 `align_reads` 依赖于前两者。
  2. **打包任务：** 编译器将每个函数调用打包成一个**任务对象**，包含任务ID、函数名、参数等信息。
  3. **分发到队列：** 编译器将 `download_genome` 和 `download_reads` 这两个任务对象提交到一个**共享的任务队列**（例如，使用 Redis 或 RabbitMQ）。

  **步骤二：调度器与沙盒执行**
  
  1. **调度器监听：** 调度器持续监听任务队列。
  2. **获取任务：** 它发现队列中有两个任务，于是将它们分别分配给**两个不同的、空闲的沙盒**。
  3. **沙盒执行：**
     - **沙盒 A** 接收到 `download_genome` 任务。它在自己的隔离环境中执行 `download_genome("yeast")`。
     - **沙盒 B** 接收到 `download_reads` 任务。它在自己的隔离环境中执行 `download_reads("yeast_data")`。
  4. **结果返回：** 两个沙盒在任务完成后，将结果（如文件路径）连同任务ID一起返回给中央调度器。
  
  **步骤三：依赖任务的执行**
  
  1. **结果存储：** 调度器收到沙盒 A 和 B 的结果后，将它们存储在一个**共享状态管理器**中（例如，一个键值存储）。
  2. **检查依赖：** 调度器会检查 `align_reads` 任务。由于它的依赖 (`result1` 和 `result2`) 现在都已完成并可用，调度器会创建 `align_reads` 的任务对象，并将其提交到任务队列。
  3. **再次分发：** 调度器将该任务分配给一个可用的沙盒（可能是 A、B，或一个新的沙盒 C）来执行。

   3. 关键技术
  
  - **任务队列 / 消息队列：** 例如 **Celery + RabbitMQ/Redis**。Celery 作为一个分布式任务队列框架，非常适合处理这种异步和并行的工作流。
  - **API / 远程过程调用 (RPC)：** 沙盒执行器和调度器之间需要通过 API 或 RPC 协议（如 gRPC）进行通信，以发送任务和接收结果。
  - **共享状态存储：** 任务执行结果和中间状态需要一个共享的地方来存储，以便后续任务能够访问。**Redis** 是一个理想的选择。

  这种设计将 **“任务执行”** 和 **“AI 规划”** 彻底解耦，并允许你将任务分发到任意数量的沙盒中。这不仅解决了跨沙盒的并行问题，还极大地增强了系统的**可伸缩性**和**安全性**。即使一个沙盒崩溃，也只会影响该容器内的任务，而不会影响整个系统。

### 3.5 工具创作与自我进化

为了使 Agent 系统具备更高级的自主性和适应性，我们引入“工具创作”能力。该功能允许 Agent 在面对现有工具集无法解决的复杂或新颖问题时，自主地生成、测试、并注册新工具，从而实现系统的自我进化。

- 核心流程

  工具创作是一个动态循环，它与规划、执行和纠错阶段紧密集成。

  1. 需求识别与创作规划：

     当 Agent 的规划或纠错环节发现现有工具无法满足任务需求时，它会进入“创作模式”。LLM 会生成一个工具创作计划，这是一个元任务图，其目标是创建一个新工具。该计划包括：

     - **功能分析**：定义新工具的具体功能、输入参数和预期输出。

     - **代码生成**：根据功能定义，LLM 在严格隔离的沙盒中编写 Python 代码。

     - **测试用例生成**：Agent 自行编写测试用例，以验证新工具的正确性。

  2. 代码生成与静态分析：

     Agent 生成的代码必须遵循严格的白名单和安全规范。编译器后端将对生成的代码进行静态分析（Static Analysis），通过解析抽象语法树（AST）确保其不包含任何不安全的语法或函数调用（例如 import os、eval() 等）。任何违反规范的代码都将被拒绝，并触发纠错反馈给 LLM。

  3. 隔离测试与动态验证：

     通过静态分析的代码不会直接被注册使用。相反，它会被打包并部署到一个临时的、完全隔离的测试沙盒中。Agent 随后会在此沙盒中执行它自己生成的测试用例。

  - 如果测试通过，系统会确认新工具的可靠性。

  - 如果测试失败，详细的错误信息将作为上下文返回给 LLM，让其进入**纠错循环**，重新生成和修复代码，直至测试通过。为了防止无限循环，我们会设定最大重试次数。

  4. 动态注册与知识更新：

     一旦新工具通过所有测试，它将以其函数签名、参数类型和功能描述等元数据，动态地注册到 Agent 的工具库中。同时，LLM 的上下文也会被更新，包含新工具的信息，以便其在未来的规划中可以立即使用。此后，Agent 将从失败点继续执行原始任务，利用新创造的工具来完成工作。

## 技术选型

- LangGraph
- graphlib
- Docker
